use crate::app::AuthState;
use oauth2::{
    AuthUrl, AuthorizationCode, ClientId, CsrfToken, PkceCodeChallenge,
    RedirectUrl, Scope, TokenUrl, TokenResponse,
};
use oauth2::basic::BasicClient;
use oauth2::reqwest::async_http_client;
use base64::Engine;
use std::collections::HashMap;
use std::io::{B<PERSON><PERSON><PERSON>, BufReader, Write};
use std::net::{TcpListener, TcpStream};
use std::sync::{Arc, Mutex};
use std::sync::atomic::{AtomicBool, Ordering};
use std::thread;
use std::time::Duration;
use url::Url;

const KEYCLOAK_URL: &str = "http://localhost:30092";
const REALM: &str = "warda";
const CLIENT_ID: &str = "warda-frontend";
const REDIRECT_PORT: u16 = 8888;
const REDIRECT_URI: &str = "http://localhost:8888/callback";

pub struct NativeAuthenticator {
    client: BasicClient,
    auth_state: Arc<Mutex<Option<AuthState>>>,
    cancelled: Arc<std::sync::atomic::AtomicBool>,
}

impl NativeAuthenticator {
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let auth_url = AuthUrl::new(format!("{}/realms/{}/protocol/openid-connect/auth", KEYCLOAK_URL, REALM))?;
        let token_url = TokenUrl::new(format!("{}/realms/{}/protocol/openid-connect/token", KEYCLOAK_URL, REALM))?;
        
        let client = BasicClient::new(
            ClientId::new(CLIENT_ID.to_string()),
            None, // No client secret for public client
            auth_url,
            Some(token_url),
        )
        .set_redirect_uri(RedirectUrl::new(REDIRECT_URI.to_string())?);

        Ok(Self {
            client,
            auth_state: Arc::new(Mutex::new(None)),
            cancelled: Arc::new(AtomicBool::new(false)),
        })
    }

    pub async fn authenticate(&self) -> Result<AuthState, Box<dyn std::error::Error>> {
        log::info!("Starting native OAuth2 authentication flow");

        // Reset cancellation state for new authentication attempt
        self.reset_cancellation();

        // Generate PKCE challenge
        let (pkce_challenge, pkce_verifier) = PkceCodeChallenge::new_random_sha256();

        // Generate the authorization URL
        let (auth_url, csrf_token) = self
            .client
            .authorize_url(CsrfToken::new_random)
            .add_scope(Scope::new("openid".to_string()))
            .add_scope(Scope::new("profile".to_string()))
            .add_scope(Scope::new("email".to_string()))
            .set_pkce_challenge(pkce_challenge)
            .url();

        log::info!("Opening browser for authentication: {}", auth_url);

        // Open the authorization URL in the user's browser
        if let Err(e) = webbrowser::open(auth_url.as_str()) {
            log::error!("Failed to open browser: {}", e);
            return Err(format!("Failed to open browser: {}", e).into());
        }

        // Start local server to receive the callback
        let auth_code = self.start_callback_server(csrf_token).await?;

        log::info!("Received authorization code, exchanging for token");

        // Exchange the authorization code for an access token
        let token_result = self
            .client
            .exchange_code(auth_code)
            .set_pkce_verifier(pkce_verifier)
            .request_async(async_http_client)
            .await?;

        let access_token = token_result.access_token().secret();
        log::info!("Successfully obtained access token");

        // Get user info from Keycloak
        let user_info = self.get_user_info(access_token).await?;

        let auth_state = AuthState {
            is_authenticated: true,
            username: user_info.get("preferred_username")
                .and_then(|v| v.as_str())
                .map(|s| s.to_string()),
            email: user_info.get("email")
                .and_then(|v| v.as_str())
                .map(|s| s.to_string()),
            token: Some(access_token.to_string()),
        };

        // Store the auth state
        {
            let mut state = self.auth_state.lock().unwrap();
            *state = Some(auth_state.clone());
        }

        log::info!("Authentication completed successfully");
        Ok(auth_state)
    }

    async fn start_callback_server(&self, expected_csrf: CsrfToken) -> Result<AuthorizationCode, Box<dyn std::error::Error>> {
        let listener = TcpListener::bind(format!("127.0.0.1:{}", REDIRECT_PORT))?;
        log::info!("Started callback server on port {}", REDIRECT_PORT);

        // Set a timeout for the server
        listener.set_nonblocking(true)?;

        let start_time = std::time::Instant::now();
        let timeout = Duration::from_secs(300); // 5 minutes timeout

        loop {
            // Check for cancellation
            if self.is_cancelled() {
                log::info!("Authentication cancelled by user");
                return Err("Authentication cancelled by user".into());
            }

            if start_time.elapsed() > timeout {
                return Err("Authentication timeout".into());
            }

            match listener.accept() {
                Ok((stream, _)) => {
                    log::info!("Received callback connection");
                    return self.handle_callback(stream, expected_csrf);
                }
                Err(ref e) if e.kind() == std::io::ErrorKind::WouldBlock => {
                    // No connection yet, sleep and try again
                    thread::sleep(Duration::from_millis(100));
                    continue;
                }
                Err(e) => return Err(e.into()),
            }
        }
    }

    fn handle_callback(&self, mut stream: TcpStream, expected_csrf: CsrfToken) -> Result<AuthorizationCode, Box<dyn std::error::Error>> {
        let mut reader = BufReader::new(&stream);
        let mut request_line = String::new();
        reader.read_line(&mut request_line)?;

        log::info!("Received callback request: {}", request_line.trim());

        // Parse the request line to extract the callback URL
        let parts: Vec<&str> = request_line.split_whitespace().collect();
        if parts.len() < 2 {
            return Err("Invalid HTTP request".into());
        }

        let path_and_query = parts[1];
        let full_url = format!("http://localhost:{}{}", REDIRECT_PORT, path_and_query);
        let url = Url::parse(&full_url)?;

        // Extract query parameters
        let query_pairs: HashMap<String, String> = url.query_pairs().into_owned().collect();

        // Send success response
        let response = if query_pairs.contains_key("code") {
            "HTTP/1.1 200 OK\r\n\r\n<html><body><h1>Authentication Successful!</h1><p>You can close this window and return to the application.</p></body></html>"
        } else {
            "HTTP/1.1 400 Bad Request\r\n\r\n<html><body><h1>Authentication Failed!</h1><p>No authorization code received.</p></body></html>"
        };

        stream.write_all(response.as_bytes())?;
        stream.flush()?;

        // Verify CSRF token
        if let Some(state) = query_pairs.get("state") {
            if state != expected_csrf.secret() {
                return Err("CSRF token mismatch".into());
            }
        } else {
            return Err("No CSRF token in callback".into());
        }

        // Extract authorization code
        if let Some(code) = query_pairs.get("code") {
            log::info!("Successfully extracted authorization code");
            Ok(AuthorizationCode::new(code.clone()))
        } else if let Some(error) = query_pairs.get("error") {
            let error_description = query_pairs.get("error_description")
                .map(|s| s.as_str())
                .unwrap_or("Unknown error");
            Err(format!("OAuth error: {} - {}", error, error_description).into())
        } else {
            Err("No authorization code or error in callback".into())
        }
    }

    async fn get_user_info(&self, access_token: &str) -> Result<serde_json::Value, Box<dyn std::error::Error>> {
        let client = reqwest::Client::new();
        let userinfo_url = format!("{}/realms/{}/protocol/openid-connect/userinfo", KEYCLOAK_URL, REALM);

        let response = client
            .get(&userinfo_url)
            .bearer_auth(access_token)
            .send()
            .await?;

        if response.status().is_success() {
            let user_info: serde_json::Value = response.json().await?;
            log::info!("Retrieved user info: {:?}", user_info);
            Ok(user_info)
        } else {
            let error_text = response.text().await?;
            Err(format!("Failed to get user info: {}", error_text).into())
        }
    }

    pub fn get_current_auth_state(&self) -> Option<AuthState> {
        self.auth_state.lock().unwrap().clone()
    }

    /// Cancel the ongoing authentication process
    pub fn cancel(&self) {
        log::info!("Cancelling authentication process");
        self.cancelled.store(true, Ordering::SeqCst);
    }

    /// Check if authentication has been cancelled
    pub fn is_cancelled(&self) -> bool {
        self.cancelled.load(Ordering::SeqCst)
    }

    /// Reset cancellation state for new authentication attempt
    pub fn reset_cancellation(&self) {
        self.cancelled.store(false, Ordering::SeqCst);
    }

    /// Validate a stored token against Keycloak
    pub async fn validate_token(&self, token: &str) -> Result<bool, Box<dyn std::error::Error>> {
        log::info!("Validating stored token against Keycloak");

        let client = reqwest::Client::new();
        let userinfo_url = format!("{}/realms/{}/protocol/openid-connect/userinfo", KEYCLOAK_URL, REALM);

        // Try to get user info with the token - if this succeeds, token is valid
        let response = client
            .get(&userinfo_url)
            .bearer_auth(token)
            .timeout(std::time::Duration::from_secs(5)) // Short timeout for startup validation
            .send()
            .await?;

        let is_valid = response.status().is_success();
        log::info!("Token validation result: {}", is_valid);

        Ok(is_valid)
    }

    /// Validate stored authentication state
    pub async fn validate_stored_auth_state(&self, auth_state: &AuthState) -> Result<bool, Box<dyn std::error::Error>> {
        if !auth_state.is_authenticated {
            return Ok(false);
        }

        let Some(ref token) = auth_state.token else {
            log::warn!("Authentication state claims to be authenticated but has no token");
            return Ok(false);
        };

        // Check if token is expired (basic JWT parsing)
        if let Err(e) = self.check_token_expiration(token) {
            log::warn!("Token appears to be expired: {}", e);
            return Ok(false);
        }

        // Validate token against Keycloak
        match self.validate_token(token).await {
            Ok(is_valid) => Ok(is_valid),
            Err(e) => {
                log::error!("Failed to validate token against Keycloak: {}", e);
                // If we can't reach Keycloak, we should not trust the stored state
                Ok(false)
            }
        }
    }

    /// Basic JWT token expiration check
    fn check_token_expiration(&self, token: &str) -> Result<(), Box<dyn std::error::Error>> {
        // Split JWT token into parts
        let parts: Vec<&str> = token.split('.').collect();
        if parts.len() != 3 {
            return Err("Invalid JWT format".into());
        }

        // Decode the payload (second part)
        let payload = parts[1];

        // Add padding if needed for base64 decoding
        let padded_payload = match payload.len() % 4 {
            0 => payload.to_string(),
            n => format!("{}{}", payload, "=".repeat(4 - n)),
        };

        let decoded = base64::engine::general_purpose::STANDARD.decode(&padded_payload)
            .map_err(|e| format!("Failed to decode JWT payload: {}", e))?;

        let payload_json: serde_json::Value = serde_json::from_slice(&decoded)
            .map_err(|e| format!("Failed to parse JWT payload JSON: {}", e))?;

        // Check expiration time
        if let Some(exp) = payload_json.get("exp").and_then(|v| v.as_i64()) {
            let now = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs() as i64;

            if now >= exp {
                return Err(format!("Token expired at {}, current time is {}", exp, now).into());
            }
        } else {
            log::warn!("JWT token has no expiration time");
        }

        Ok(())
    }

    pub fn logout(&self) {
        let mut state = self.auth_state.lock().unwrap();
        *state = None;
        log::info!("User logged out");
    }
}
